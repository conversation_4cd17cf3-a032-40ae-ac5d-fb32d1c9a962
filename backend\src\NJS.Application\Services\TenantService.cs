using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NJS.Domain.Database;
using NJS.Domain.Entities;
using NJS.Repositories.Interfaces;
using System.Security.Claims;

namespace NJS.Application.Services
{
    public class TenantService : ITenantService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly TenantDbContext _tenantDbContext;

        public int? TenantId { get; set; }

        public TenantService(IHttpContextAccessor httpContextAccessor, TenantDbContext tenantDbContext)
        {
            _httpContextAccessor = httpContextAccessor;
            _tenantDbContext = tenantDbContext;
        }

        public async Task<string> GetTenantDomain()
        {
            return _httpContextAccessor.HttpContext?.Request.Host.Value ?? string.Empty;
        }

        public async Task<int?> GetTenantId(string domain)
        {
            var tenant = await _tenantDbContext.Tenants.FirstOrDefaultAsync(t => t.Domain == domain);
            return tenant?.Id;
        }

        public async Task<int?> GetCurrentTenantIdAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
                return null;

            // First, try to get tenant ID from JWT claims (most reliable)
            var tenantIdFromClaims = GetTenantIdFromClaims();
            if (tenantIdFromClaims.HasValue && tenantIdFromClaims.Value > 0)
            {
                return tenantIdFromClaims.Value;
            }

            // Check if tenant ID is already set in the context
            if (httpContext.Items.TryGetValue("TenantId", out var tenantId))
            {
                return tenantId as int?;
            }

            var tenantDomain = await GetCurrentTenantDomain();
            if (string.IsNullOrEmpty(tenantDomain))
                return null;

            var tenant = await _tenantDbContext.Tenants.FirstOrDefaultAsync(t => t.Domain == tenantDomain);
            if (tenant != null)
            {
                httpContext.Items["TenantId"] = tenant.Id;
                httpContext.Items["TenantDomain"] = tenant.Domain;
                return tenant.Id;
            }

            return null;
        }

        public async Task<string> GetCurrentTenantDomain()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
                return string.Empty;

            // First, try to get tenant domain from JWT claims
            var tenantDomainFromClaims = GetTenantDomainFromClaims();
            if (!string.IsNullOrEmpty(tenantDomainFromClaims))
            {
                return tenantDomainFromClaims;
            }

            var tenantContext = httpContext.Request.Headers["X-Tenant-Context"].FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantContext))
            {
                return tenantContext;
            }

            // Fallback to host-based tenant resolution
            return httpContext.Request.Host.Value ?? string.Empty;
        }

        public async Task<bool> SetTenantContextAsync(string tenantDomain)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
                return false;

            var tenant = await _tenantDbContext.Tenants.FirstOrDefaultAsync(t => t.Domain == tenantDomain);
            if (tenant == null)
            {
                return false;
            }

            httpContext.Items["TenantId"] = tenant.Id;
            httpContext.Items["TenantDomain"] = tenant.Domain;
            httpContext.Items["Tenant"] = tenant;

            TenantId = tenant.Id;

            return true;
        }

        // New methods for JWT claim extraction
        public int? GetTenantIdFromClaims()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated != true)
                return null;

            var tenantIdClaim = httpContext.User.FindFirst("TenantId");
            if (tenantIdClaim != null && int.TryParse(tenantIdClaim.Value, out var tenantId))
            {
                return tenantId;
            }

            return null;
        }

        public string GetTenantDomainFromClaims()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated != true)
                return string.Empty;

            var tenantDomainClaim = httpContext.User.FindFirst("TenantDomain");
            return tenantDomainClaim?.Value ?? string.Empty;
        }

        public string GetUserTypeFromClaims()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated != true)
                return string.Empty;

            var userTypeClaim = httpContext.User.FindFirst("UserType");
            return userTypeClaim?.Value ?? string.Empty;
        }

        public string GetTenantRoleFromClaims()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated != true)
                return string.Empty;

            var tenantRoleClaim = httpContext.User.FindFirst("TenantRole");
            return tenantRoleClaim?.Value ?? string.Empty;
        }

        public bool IsSuperAdminFromClaims()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated != true)
                return false;

            var isSuperAdminClaim = httpContext.User.FindFirst("IsSuperAdmin");
            return isSuperAdminClaim?.Value == "true";
        }

        public async Task<Tenant> GetCurrentTenantAsync()
        {
            var tenantId = await GetCurrentTenantIdAsync();
            if (!tenantId.HasValue)
                return null;

            return await _tenantDbContext.Tenants.FirstOrDefaultAsync(t => t.Id == tenantId.Value);
        }

        public async Task<bool> ValidateTenantAccessAsync(string userId, int tenantId)
        {
            // For super admin, allow access to any tenant
            if (IsSuperAdminFromClaims())
                return true;

            // For regular users, check if they belong to the tenant
            // This would query the TenantUser table
            // For now, return true (implement actual validation)
            return true;
        }
    }
}
